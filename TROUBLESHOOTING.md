# Troubleshooting Installation Issues

## Common Problems and Solutions

### 1. pywin32 / win32com.client Issues

#### Problem: "No module named 'win32com'"
**Solutions (try in order):**

```cmd
# Method 1: Standard installation
pip install pywin32==306

# Method 2: If Method 1 fails, try alternative package
pip uninstall pywin32
pip install pypiwin32==223

# Method 3: Manual post-install (run after successful pip install)
python Scripts/pywin32_postinstall.py -install

# Method 4: Force reinstall
pip uninstall pywin32 pypiwin32
pip install --force-reinstall --no-cache-dir pywin32==306
```

#### Problem: "Access denied" during pywin32 installation
**Solution:** Run Command Prompt as Administrator

#### Problem: pywin32 installs but win32com.client still not found
**Solution:**
```cmd
# Find your Python installation path
python -c "import sys; print(sys.prefix)"

# Then run (replace PATH with your Python path):
python PATH\Scripts\pywin32_postinstall.py -install
```

### 2. netifaces Issues

#### Problem: "Microsoft Visual C++ 14.0 is required"
**Solutions:**

1. **Install Microsoft C++ Build Tools:**
   - Download: https://visualstudio.microsoft.com/visual-cpp-build-tools/
   - Install "C++ build tools" workload

2. **Or install pre-compiled wheel:**
   ```cmd
   pip install --only-binary=all netifaces==0.11.0
   ```

3. **Try older version:**
   ```cmd
   pip install netifaces==0.10.9
   ```

4. **Use alternative package:**
   ```cmd
   pip uninstall netifaces
   pip install netifaces2==0.0.22
   ```

#### Problem: netifaces compilation fails
**Solutions:**
```cmd
# Method 1: Clear cache and retry
pip cache purge
pip install --no-cache-dir netifaces==0.11.0

# Method 2: Use pre-compiled wheel
pip install --find-links https://www.lfd.uci.edu/~gohlke/pythonlibs/ netifaces

# Method 3: Install from conda (if you have conda)
conda install -c conda-forge netifaces
```

### 3. Alternative Code (if packages still fail)

If you can't install the packages, here's a modified version that uses only built-in Windows tools:

#### Create: `hotspot_app_minimal.py`
```python
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os

# Simplified version without external dependencies
class HotspotAppMinimal:
    def __init__(self, root):
        self.root = root
        self.root.title("Hotspot Manager (Minimal)")
        self.root.geometry("500x300")
        
        # Check if running as admin (simplified check)
        try:
            os.listdir(os.sep.join([os.environ.get('SystemRoot','C:\\windows'),'temp']))
        except:
            messagebox.showerror("Error", "Please run as Administrator")
            sys.exit()
        
        # Create tabs
        self.tab_control = ttk.Notebook(root)
        self.tab1 = ttk.Frame(self.tab_control)
        self.tab_control.add(self.tab1, text='Hotspot Control')
        self.tab_control.pack(expand=1, fill="both")
        
        self.setup_hotspot_tab()
    
    def setup_hotspot_tab(self):
        # SSID
        tk.Label(self.tab1, text="SSID:").grid(row=0, column=0, padx=10, pady=10)
        self.ssid_entry = tk.Entry(self.tab1, width=25)
        self.ssid_entry.grid(row=0, column=1, padx=10, pady=10)
        self.ssid_entry.insert(0, "MyHotspot")
        
        # Password
        tk.Label(self.tab1, text="Password:").grid(row=1, column=0, padx=10, pady=10)
        self.password_entry = tk.Entry(self.tab1, width=25, show="*")
        self.password_entry.grid(row=1, column=1, padx=10, pady=10)
        self.password_entry.insert(0, "Password123")
        
        # Status
        self.status_var = tk.StringVar(value="Status: Stopped")
        tk.Label(self.tab1, textvariable=self.status_var).grid(row=2, column=0, columnspan=2, pady=10)
        
        # Toggle Button
        self.toggle_btn = tk.Button(
            self.tab1, 
            text="Start Hotspot", 
            command=self.toggle_hotspot,
            width=15
        )
        self.toggle_btn.grid(row=3, column=0, columnspan=2, pady=10)
    
    def toggle_hotspot(self):
        ssid = self.ssid_entry.get()
        password = self.password_entry.get()
        
        if len(password) < 8:
            messagebox.showerror("Error", "Password must be at least 8 characters")
            return
        
        try:
            if "Stopped" in self.status_var.get():
                # Start hotspot
                subprocess.run(f'netsh wlan set hostednetwork mode=allow ssid="{ssid}" key="{password}"', shell=True, check=True)
                subprocess.run('netsh wlan start hostednetwork', shell=True, check=True)
                self.status_var.set("Status: Running")
                self.toggle_btn.config(text="Stop Hotspot")
            else:
                # Stop hotspot
                subprocess.run('netsh wlan stop hostednetwork', shell=True, check=True)
                self.status_var.set("Status: Stopped")
                self.toggle_btn.config(text="Start Hotspot")
        except subprocess.CalledProcessError:
            messagebox.showerror("Error", "Failed to configure hotspot. Make sure WiFi adapter supports hosted networks.")

if __name__ == "__main__":
    root = tk.Tk()
    app = HotspotAppMinimal(root)
    root.mainloop()
```

### 4. Step-by-Step Manual Installation

If automated scripts fail, try manual installation:

```cmd
# 1. Open Command Prompt as Administrator
# 2. Update pip
python -m pip install --upgrade pip setuptools wheel

# 3. Install each package individually
pip install psutil==5.9.8
pip install pyinstaller==6.3.0

# 4. For pywin32 - try different approaches:
pip install pywin32==306
# OR
pip install pypiwin32==223

# 5. For netifaces - try different approaches:
pip install netifaces==0.11.0
# OR
pip install netifaces==0.10.9
# OR
pip install netifaces2==0.0.22

# 6. Test imports
python -c "import win32com.client; print('win32com OK')"
python -c "import netifaces; print('netifaces OK')"
```

### 5. Building Without Problematic Dependencies

If you still can't install the packages, you can build with the minimal version:

```cmd
pyinstaller --onefile --windowed --name=HotspotManager hotspot_app_minimal.py
```

This will create a basic hotspot manager that can start/stop hotspots but won't have the advanced features like device listing and connection sharing.

## Getting Help

If none of these solutions work:
1. Check your Python version: `python --version` (should be 3.8+)
2. Check if you're using 32-bit or 64-bit Python: `python -c "import platform; print(platform.architecture())"`
3. Try installing in a virtual environment:
   ```cmd
   python -m venv hotspot_env
   hotspot_env\Scripts\activate
   pip install -r requirements.txt
   ```
