@echo off
echo ========================================
echo Hotspot Manager Build Process
echo ========================================

echo.
echo Installing dependencies with error handling...
call install_dependencies.bat

echo.
echo Testing if all dependencies are available...
python -c "
import sys
failed = []
try:
    import win32com.client
    print('✓ win32com.client imported successfully')
except ImportError:
    failed.append('win32com.client')
    print('✗ win32com.client failed')

try:
    import netifaces
    print('✓ netifaces imported successfully')
except ImportError:
    failed.append('netifaces')
    print('✗ netifaces failed')

if failed:
    print(f'\\nSome dependencies failed: {failed}')
    print('You can either:')
    print('1. Check TROUBLESHOOTING.md for solutions')
    print('2. Use the minimal version (hotspot_app_minimal.py)')
    sys.exit(1)
else:
    print('\\nAll dependencies OK! Proceeding with build...')
"

if %errorlevel% neq 0 (
    echo.
    echo Dependencies check failed. See TROUBLESHOOTING.md
    pause
    exit /b 1
)

echo.
echo Building executable...
python build_exe.py

echo.
echo Build complete! Check dist folder for HotspotManager.exe
pause
