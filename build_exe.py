"""
Build script for creating executable from hotspot_app.py
"""
import subprocess
import sys
import os

def build_executable():
    """Build the executable using PyInstaller"""
    
    # PyInstaller command with options
    cmd = [
        'pyinstaller',
        '--onefile',                    # Create single executable file
        '--windowed',                   # Hide console window
        '--name=HotspotManager',        # Name of the executable
        '--icon=hotspot.ico',           # Icon file (optional)
        '--add-data=hotspot.ico;.',     # Include icon in bundle (optional)
        '--hidden-import=win32com.client',
        '--hidden-import=netifaces',
        '--hidden-import=psutil',
        '--clean',                      # Clean cache before building
        'hotspot_app.py'
    ]
    
    # Remove icon-related options if icon file doesn't exist
    if not os.path.exists('hotspot.ico'):
        cmd = [arg for arg in cmd if not arg.startswith('--icon') and not arg.startswith('--add-data')]
    
    print("Building executable...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Build successful!")
        print(f"Executable created at: dist/HotspotManager.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

if __name__ == "__main__":
    build_executable()
