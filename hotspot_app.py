import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import win32com.client
import netifaces
import psutil
import re
import threading
import time
import sys

def is_admin():
    """Check if running as admin"""
    try:
        return win32com.client.Dispatch("WScript.Shell").RegRead(
            "HKEY_USERS\\S-1-5-19\\Environment\\TEMP"
        ) is not None
    except:
        return False

def run_as_admin():
    """Relaunch as admin"""
    shell = win32com.client.Dispatch("WScript.Shell")
    shell.ShellExecute("python.exe", f'"{sys.argv[0]}"', "", "runas", 1)
    sys.exit()

class HotspotApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Hotspot Manager")
        self.root.geometry("600x400")
        self.root.resizable(True, True)
        
        # Check admin rights
        if not is_admin():
            self.root.withdraw()
            run_as_admin()
            return
        
        # Tab setup
        self.tab_control = ttk.Notebook(root)
        self.tab1 = ttk.Frame(self.tab_control)
        self.tab2 = ttk.Frame(self.tab_control)
        self.tab3 = ttk.Frame(self.tab_control)
        
        self.tab_control.add(self.tab1, text='Hotspot')
        self.tab_control.add(self.tab2, text='Connected Devices')
        self.tab_control.add(self.tab3, text='Internet Sharing')
        self.tab_control.pack(expand=1, fill="both")
        
        # Tab 1: Hotspot Control
        self.setup_hotspot_tab()
        
        # Tab 2: Connected Devices
        self.setup_devices_tab()
        
        # Tab 3: Internet Sharing
        self.setup_sharing_tab()
        
        # Device list updater
        self.update_device_list()

    def setup_hotspot_tab(self):
        # SSID
        tk.Label(self.tab1, text="SSID:").grid(row=0, column=0, padx=10, pady=10)
        self.ssid_entry = tk.Entry(self.tab1, width=25)
        self.ssid_entry.grid(row=0, column=1, padx=10, pady=10)
        self.ssid_entry.insert(0, "MyHotspot")
        
        # Password
        tk.Label(self.tab1, text="Password:").grid(row=1, column=0, padx=10, pady=10)
        self.password_entry = tk.Entry(self.tab1, width=25, show="*")
        self.password_entry.grid(row=1, column=1, padx=10, pady=10)
        self.password_entry.insert(0, "Password123")
        
        # Status
        self.status_var = tk.StringVar(value="Status: Stopped")
        tk.Label(self.tab1, textvariable=self.status_var).grid(row=2, column=0, columnspan=2, pady=10)
        
        # Toggle Button
        self.toggle_btn = tk.Button(
            self.tab1, 
            text="Start Hotspot", 
            command=self.toggle_hotspot,
            width=15
        )
        self.toggle_btn.grid(row=3, column=0, columnspan=2, pady=10)

    def setup_devices_tab(self):
        # Device list
        columns = ("IP Address", "MAC Address", "Hostname")
        self.device_tree = ttk.Treeview(self.tab2, columns=columns, show="headings")
        
        for col in columns:
            self.device_tree.heading(col, text=col)
            self.device_tree.column(col, width=150)
        
        self.device_tree.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Refresh button
        tk.Button(
            self.tab2, 
            text="Refresh", 
            command=self.update_device_list
        ).pack(pady=10)

    def setup_sharing_tab(self):
        # Source selection
        tk.Label(self.tab3, text="Share this connection:").pack(pady=(20, 5))
        
        self.connection_var = tk.StringVar()
        connections = self.get_network_connections()
        if not connections:
            connections = ["No active connections found"]
        
        self.connection_menu = ttk.Combobox(
            self.tab3, 
            textvariable=self.connection_var,
            values=connections,
            state="readonly",
            width=40
        )
        self.connection_menu.pack(pady=10)
        self.connection_menu.current(0)
        
        # Apply button
        tk.Button(
            self.tab3, 
            text="Apply Sharing", 
            command=self.apply_sharing,
            width=15
        ).pack(pady=20)

    def toggle_hotspot(self):
        ssid = self.ssid_entry.get()
        password = self.password_entry.get()
        
        if not (8 <= len(password) <= 63):
            messagebox.showerror("Error", "Password must be 8-63 characters")
            return
        
        try:
            if "Stopped" in self.status_var.get():
                # Create hotspot
                subprocess.run(
                    f'netsh wlan set hostednetwork mode=allow ssid="{ssid}" key="{password}"',
                    check=True,
                    shell=True
                )
                subprocess.run(
                    'netsh wlan start hostednetwork',
                    check=True,
                    shell=True
                )
                self.status_var.set("Status: Running")
                self.toggle_btn.config(text="Stop Hotspot")
            else:
                # Stop hotspot
                subprocess.run(
                    'netsh wlan stop hostednetwork',
                    check=True,
                    shell=True
                )
                self.status_var.set("Status: Stopped")
                self.toggle_btn.config(text="Start Hotspot")
        except subprocess.CalledProcessError as e:
            messagebox.showerror("Error", f"Failed to configure hotspot:\n{str(e)}")

    def get_network_connections(self):
        connections = []
        for iface in netifaces.interfaces():
            addrs = netifaces.ifaddresses(iface)
            if netifaces.AF_INET in addrs and not iface.startswith('Loopback'):
                try:
                    iface_name = psutil.net_if_addrs()[iface][0].description
                    if iface_name not in connections:
                        connections.append(iface_name)
                except:
                    continue
        return connections

    def get_connected_devices(self):
        try:
            output = subprocess.check_output(
                'netsh wlan show hostednetwork',
                shell=True,
                text=True
            )
            
            devices = []
            mac_pattern = r"([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})"
            
            for line in output.splitlines():
                if "IP address" in line:
                    ip = line.split(":")[1].strip()
                elif re.match(mac_pattern, line):
                    mac = line.strip()
                    devices.append((ip, mac, "Unknown"))
            
            return devices
        except:
            return []

    def update_device_list(self):
        for item in self.device_tree.get_children():
            self.device_tree.delete(item)
        
        devices = self.get_connected_devices()
        for device in devices:
            self.device_tree.insert("", "end", values=device)
        
        # Auto-refresh every 30 seconds
        self.root.after(30000, self.update_device_list)

    def apply_sharing(self):
        selected = self.connection_var.get()
        if not selected or "No active" in selected:
            messagebox.showwarning("Warning", "No valid connection selected")
            return
        
        try:
            # Find hotspot interface
            hotspot_iface = None
            for iface, addrs in psutil.net_if_addrs().items():
                if "Microsoft Hosted Network Virtual Adapter" in addrs[0].description:
                    hotspot_iface = addrs[0].description
                    break
            
            if not hotspot_iface:
                messagebox.showerror("Error", "Hotspot interface not found")
                return
            
            # Configure sharing using netsh
            subprocess.run(
                f'netsh interface ipv4 set interface "{hotspot_iface}" forwarding=enabled',
                shell=True
            )
            subprocess.run(
                f'netsh interface ipv4 set interface "{selected}" forwarding=enabled',
                shell=True
            )
            
            messagebox.showinfo("Success", f"Sharing {selected} with hotspot")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to configure sharing:\n{str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = HotspotApp(root)
    root.mainloop()