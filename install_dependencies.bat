@echo off
echo ========================================
echo Installing Hotspot App Dependencies
echo ========================================

echo.
echo Step 1: Upgrading pip...
python -m pip install --upgrade pip

echo.
echo Step 2: Installing Microsoft Visual C++ Build Tools dependencies...
pip install setuptools wheel

echo.
echo Step 3: Installing pywin32 (Windows COM support)...
pip install pywin32==306
if %errorlevel% neq 0 (
    echo pywin32 failed, trying alternative...
    pip install pypiwin32==223
)

echo.
echo Step 4: Installing netifaces (Network interfaces)...
pip install netifaces==0.11.0
if %errorlevel% neq 0 (
    echo netifaces failed, trying to install with --no-cache-dir...
    pip install --no-cache-dir netifaces==0.11.0
    if %errorlevel% neq 0 (
        echo Still failed, trying alternative version...
        pip install netifaces==0.10.9
        if %errorlevel% neq 0 (
            echo All netifaces versions failed, trying netifaces2...
            pip install netifaces2==0.0.22
        )
    )
)

echo.
echo Step 5: Installing other dependencies...
pip install psutil==5.9.8
pip install pyinstaller==6.3.0

echo.
echo Step 6: Post-install configuration for pywin32...
python -c "import sys; import os; os.system('python ' + sys.prefix + '/Scripts/pywin32_postinstall.py -install')" 2>nul

echo.
echo ========================================
echo Installation Summary
echo ========================================
python -c "
try:
    import win32com.client
    print('✓ win32com.client - OK')
except ImportError as e:
    print('✗ win32com.client - FAILED:', e)

try:
    import netifaces
    print('✓ netifaces - OK')
except ImportError as e:
    print('✗ netifaces - FAILED:', e)

try:
    import psutil
    print('✓ psutil - OK')
except ImportError as e:
    print('✗ psutil - FAILED:', e)

try:
    import PyInstaller
    print('✓ PyInstaller - OK')
except ImportError as e:
    print('✗ PyInstaller - FAILED:', e)
"

echo.
echo If any packages failed, see TROUBLESHOOTING.md for manual solutions
pause
